react-router-dom.js?v=dad241c9:4393 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=dad241c9:4393
react-router-dom.js?v=dad241c9:4393 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=dad241c9:4393
partytown-sandbox-sw.html?1750810774878:2 @builder.io/partytown package has changed organization and now is
@qwik.dev/partytown https://www.npmjs.com/package/@qwik.dev/partytown
We recommend using the new package to stay up to date on feature releases and bug fixes.
(anonymous) @ partytown-sandbox-sw.html?1750810774878:2
